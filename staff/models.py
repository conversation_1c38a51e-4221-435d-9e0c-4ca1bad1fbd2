import bcrypt
from django.db import models
from django.utils import timezone

from core.generate_resource_id import generate_staff_id
from core.model import BaseModel
from core.soft_delete import AllObjectsManager, SoftDeleteManager
from staff.enums import Department, Gender, Position, Region


# 员工模型
class Staff(BaseModel):
    # 员工姓名
    name = models.CharField(max_length=50,verbose_name='员工姓名')
    # 员工性别
    gender = models.CharField(max_length=50,choices=Gender.choices,verbose_name='员工性别')
    # 员工电话
    phone = models.CharField(max_length=50,verbose_name='员工电话')
    # 员工邮箱
    email = models.EmailField(max_length=255,verbose_name='员工邮箱')
    # 员工部门
    department = models.CharField(max_length=50,choices=Department.choices,verbose_name='员工部门')
    # 员工职位
    position = models.CharField(max_length=50,choices=Position.choices,verbose_name='员工职位')
    # 员工区域
    region = models.CharField(max_length=50,choices=Region.choices,verbose_name='员工区域')
    # 入职日期
    join_date = models.DateField(verbose_name='入职日期')
    # 合同到期日期
    contract_end_date = models.DateField(verbose_name='合同到期日期')
    # 备注
    remark = models.TextField(verbose_name='备注')
    # 密码
    password = models.CharField(max_length=128, verbose_name="密码",blank=True, default='')
    # 员工id
    uid = models.CharField(max_length=50,verbose_name='员工id',unique=True,blank=True,default=generate_staff_id)
    # 软删除时间戳
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='删除时间')
    # 离职日期（软删除时记录）
    leave_date = models.DateField(null=True, blank=True, verbose_name='离职日期')
    # 离职原因
    leave_reason = models.TextField(blank=True, verbose_name='离职原因')
    # 最后登录时间
    last_login_time = models.DateTimeField(verbose_name="最后登录时间", blank=True, null=True)
    
    # 默认管理器（只返回未删除的记录）
    objects = SoftDeleteManager()
    # 包含所有记录的管理器
    all_objects = AllObjectsManager()

    class Meta:
        verbose_name = '员工管理'
        verbose_name_plural = '员工管理'
        constraints = [
            models.UniqueConstraint(
                fields=['uid'],
                condition=models.Q(deleted_at__isnull=True),
                name='unique_uid_when_not_deleted'
            ),
            models.UniqueConstraint(
                fields=['phone'],
                condition=models.Q(deleted_at__isnull=True),
                name='unique_phone_when_not_deleted'
            ),
            models.UniqueConstraint(
                fields=['email'],
                condition=models.Q(deleted_at__isnull=True),
                name='unique_email_when_not_deleted'
            ),
        ]
        
    # 更新最后登录时间  
    def update_last_login(self):
        self.last_login_time = timezone.now()
        self.save()

    # 设置密码
    def set_password(self, raw_password):
        self.password = bcrypt.hashpw(
            raw_password.encode("utf-8"), bcrypt.gensalt()
        ).decode("utf-8")

    # 检查密码  
    def check_password(self, raw_password):
        return bcrypt.checkpw(
            raw_password.encode("utf-8"), self.password.encode("utf-8")
        )

    def __str__(self):
        return f"{self.name}{'(已离职)' if self.deleted_at else ''}"


    # 软删除方法
    def delete(self, using=None, keep_parents=False, leave_date=None, leave_reason=''):
        if self.deleted_at: return
        self.deleted_at = timezone.now()
        self.leave_date = leave_date or timezone.now().date()
        self.leave_reason = leave_reason
        self.save(using=using)
        
    # 判断是否已被软删除
    @property
    def is_deleted(self):
        return self.deleted_at is not None

    # 检查电话号码是否已存在（只检查未删除的记录）
    @classmethod
    def check_phone_exist(cls, phone, exclude_id=None):
        queryset = cls.objects.filter(phone=phone)
        if exclude_id:
            queryset = queryset.exclude(id=exclude_id)
        return queryset.exists()

    # 检查邮箱是否已存在（只检查未删除的记录）
    @classmethod
    def check_email_exist(cls, email, exclude_id=None):
        queryset = cls.objects.filter(email=email)
        if exclude_id:
            queryset = queryset.exclude(id=exclude_id)
        return queryset.exists()
    
    # 根据uid获取员工
    @classmethod
    def get_staff_by_uid(cls, uid):
        try:
            return cls.objects.get(uid=uid)
        except cls.DoesNotExist:
            return None

