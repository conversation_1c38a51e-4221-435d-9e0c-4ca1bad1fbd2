from django.urls import path

from staff.views import StaffCreateView, StaffDetailView, StaffListView, StaffUpdateView

urlpatterns = [

    # 员工列表
    path('list/', StaffListView.as_view(), name='staff-list'),
    # 员工详情
    path('detail/<str:uid>/', StaffDetailView.as_view(), name='staff-detail'),
    # 员工创建
    path('create/', StaffCreateView.as_view(), name='staff-create'),
    # 员工更新
    path('update/<str:uid>/', StaffUpdateView.as_view(), name='staff-update'),
    
]
