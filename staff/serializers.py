import bcrypt
from rest_framework import serializers
from core.utils import encrypt_password
from staff.enums import Department, Gender, Position, Region
from staff.models import Staff


# 员工列表序列化器
class StaffListSerializer(serializers.ModelSerializer):
    

    
    class Meta:
        model = Staff
        fields = ['uid', 'name', 'gender', 'phone', 'department', 'position', 'region', 'join_date', 'remark']
        
    
    

# 员工详情序列化器
class StaffDetailSerializer(serializers.ModelSerializer):
    
    # 密码修改状态
    pwd_status = serializers.SerializerMethodField()
    
    class Meta:
        model = Staff
        fields = ['uid', 'name', 'gender', 'pwd_status','phone', 'email', 'department', 'position', 'region', 'join_date', 'contract_end_date', 'last_login_time','remark']
    
    def get_pwd_status(self, obj):
        return obj.password == encrypt_password(f'M{obj.phone}')
    

# 员工创建及更新序列化器
class StaffCreateAndUpdateSerializer(serializers.ModelSerializer):
    # 部门
    department = serializers.CharField()
    # 职位
    position = serializers.CharField()
    # 区域
    region = serializers.CharField()
    # 性别
    gender = serializers.CharField()
    # 电话
    phone = serializers.CharField()
    # 邮箱
    email = serializers.EmailField()
    # 性别
    gender = serializers.CharField()

    class Meta:
        model = Staff
        fields = ['name', 'gender', 'phone', 'email', 'department', 'position', 'region', 'join_date', 'contract_end_date','remark','password']
    
    # 验证密码
    def validate_password(self, value):
        if len(value) < 8:
            raise serializers.ValidationError("密码长度不能小于8位")
        return bcrypt.hashpw(
            value.encode("utf-8"), bcrypt.gensalt()
        ).decode("utf-8")
        
    # 验证电话是否重复
    def validate_phone(self, value):
        if Staff.check_phone_exist(value, self.instance.id if self.instance else None):
            raise serializers.ValidationError("电话号码已存在")
        return value
    
    # 验证邮箱是否重复
    def validate_email(self, value):
        if Staff.check_email_exist(value, self.instance.id if self.instance else None):
            raise serializers.ValidationError("邮箱已存在")
        return value
    
    # 验证性别
    def validate_gender(self, value):
        if value not in Gender.values:
            raise serializers.ValidationError("性别不正确")
        return value
    
    # 验证部门
    def validate_department(self, value):
        if value not in Department.values:
            raise serializers.ValidationError("部门不正确")
        return value
    
    # 验证职位
    def validate_position(self, value):
        if value not in Position.values:
            raise serializers.ValidationError("职位不正确")
        return value

    # 验证区域
    def validate_region(self, value):
        if value not in Region.values:
            raise serializers.ValidationError("区域不正确")
        return value